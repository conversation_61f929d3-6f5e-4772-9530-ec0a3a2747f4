import { defineChildRoute, defineRoute, defineRoutes } from '@/lib/defineRoute'
import { accountsEffectiveDate } from '../../routing'
import { viewAccount } from '../../view/[accountCode]/routing'

export const editAccount = defineChildRoute(
  accountsEffectiveDate,
  defineRoute('edit', ['accountCode']),
)

export const editAccountInformation = defineChildRoute(
  editAccount,
  defineRoute('account-information'),
)

export const editDemographicsAndPricing = defineChildRoute(
  editAccount,
  defineRoute('demographics-and-pricing'),
)

export const editAccountRelationships = defineChildRoute(
  editAccount,
  defineRoute('relationships'),
)

export const editSettlementAndProcessing = defineChildRoute(
  editAccount,
  defineRoute('settlement-and-processing'),
)

export const statementPackageDetail = defineChildRoute(
  viewAccount,
  defineRoute('statement-package'),
)

export const [useRoute, routeTo] = defineRoutes(
  editAccountInformation,
  editDemographicsAndPricing,
  editAccountRelationships,
  editSettlementAndProcessing,
  statementPackageDetail
)
