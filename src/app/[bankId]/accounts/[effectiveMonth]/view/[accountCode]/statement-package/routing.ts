import { defineChildRoute, defineRoute, defineRoutes } from '@/lib/defineRoute'
import { statementPackage } from '../routing'

export const statementPackageDetail = defineChildRoute(
  statementPackage,
  defineRoute('view', ['packageNumber']),
)

export const editStatementPackage = defineChildRoute(
  statementPackage,
  defineRoute('edit', ['packageNumber']),
)

export const addStatementPackage = defineChildRoute(
  statementPackage,
  defineRoute('add', []),
)

export const [useRoute, routeTo] = defineRoutes(
  statementPackageDetail,
  editStatementPackage,
  addStatementPackage
)
