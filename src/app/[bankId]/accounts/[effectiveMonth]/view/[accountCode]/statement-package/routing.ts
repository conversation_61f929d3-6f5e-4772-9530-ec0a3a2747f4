import { defineChildRoute, defineRoute, defineRoutes } from '@/lib/defineRoute'
import { statementPackage } from '../routing'

export const statementPackageDetail = defineChildRoute(
  statementPackage,
  defineRoute('view', ['packageNumber']),
)

export const editStatementPackage = defineChildRoute(
  statementPackage,
  defineRoute('edit', ['packageNumber']),
)

export const [useRoute, routeTo] = defineRoutes(
  statementPackageDetail,
  editStatementPackage,
)
