'use client'

import { useForm } from '@tanstack/react-form'
import {
  StatementPackageFormSchema,
  StatementPackageFormZodSchema,
  PackageDeliveryLabels,
  AddressCode,
  AddressFormSchema,
  EditStatementPackageFormProps,
  StatementPackageRouteParams,
} from '@/app/[bankId]/accounts/types'
import {
  InfoSection,
  InfoSectionTitle,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { formToApiSchemas, formValidators } from '@/api/formToApiSchema'
import { enumOptions } from '@/lib/validation/enumOptions'
import { data } from '@/lib/unions/Union'
import {
  addressCodeToString,
  addressToAddressCode,
  formatAddress,
  accountCodeToString,
  AddressOptionProps,
  formToAddress,
  formatAddressNumber,
  toAccountCodeString,
} from '@/app/[bankId]/accounts/accountHelpers'
import { SelectOption } from '@/components/Input/Select'
import {
  AddressModal,
  AddressModalProps,
} from '@/app/[bankId]/accounts/_components/AddressModal'
import { useExternalModalStateAndProps } from '@/components/Modal'
import { Button as HeadlessButton } from '@headlessui/react'
import { toUTCDateString } from '@/lib/date'
import { useCallback, useState, useMemo } from 'react'
import { AddressForm } from '@/api/formToApiSchema'
import { routeTo, useRoute } from '../../routing'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRouter } from 'next/navigation'
import { EditAccountFooter } from '@/app/[bankId]/accounts/[effectiveMonth]/edit/[accountCode]/_components/EditAccountFooter'
import { useMutation } from '@tanstack/react-query'
import { accountMutation } from '@/app/[bankId]/accounts/mutations'

function AddressOption(props: AddressOptionProps) {
  return (
    <div className='flex items-center justify-start gap-4'>
      <div>{formatAddress(props.address)}</div>
      <div className='flex items-center gap-2 font-light'>
        <div>
          {`${props.address.applicationId}-${formatAddressNumber(props.address.addressNumber)}`}
        </div>
        {props.fromKeyAccount && (
          <div className='text-sm'> from key account</div>
        )}
      </div>
      {props.editable ?
        <HeadlessButton
          className='ml-auto text-sm font-medium text-app-color-button-primary-bg'
          onClick={() => props.onEdit?.(props.address)}
        >
          Edit
        </HeadlessButton>
      : null}
    </div>
  )
}

export function EditStatementPackageForm({
  statementPackageDetail,
  addresses,
  statementPackages,
  onSave,
}: EditStatementPackageFormProps) {
  const route = useRoute()!
  const routeParams = data(route).params
  const { accountCode } = useAccountFromParams(routeParams)
  const router = useRouter()

  // Store initial values for reset functionality
  const initialValues = {
    effectiveDate: statementPackageDetail.statementPackage.effectiveDate,
    statementPackageNumber:
      statementPackageDetail.statementPackage.statementPackageNumber,
    packageDelivery: statementPackageDetail.statementPackage.packageDelivery,
    addressCode: statementPackageDetail.statementPackage.addressCode,
    packageType: statementPackageDetail.statementPackage.packageType,
    selectedAccounts: statementPackageDetail.selectedAccounts,
    existingStatementPackageNumbers: statementPackages.map(
      (pkg) => pkg.statementPackage.statementPackageNumber,
    ),
  }

  // Create form with proper defaults from the loaded data
  const form = useForm<StatementPackageFormSchema>({
    defaultValues: initialValues,
    validators: {
      onSubmit: StatementPackageFormZodSchema,
    },
    onSubmit: async ({ value }) => {
      onSave(value)
    },
  })

  const FormMonthPicker = useFormMonthPicker<StatementPackageFormSchema>({
    form,
  })
  const FormInput = useFormTextInput<StatementPackageFormSchema>({ form })
  const Select = useFormSelect<StatementPackageFormSchema>({ form })

  // Address modal state and management
  const [addressModalState, addressModalProps, setAddressModalProps] =
    useExternalModalStateAndProps<AddressModalProps>({})
  const [canAddNewAddress, setCanAddNewAddress] = useState<boolean>(true)

  // Mutation for creating address
  const { mutate: createAddress } = useMutation(
    accountMutation('/createAddress'),
  )

  const allAddresses = useMemo(() => {
    const addressList = [...addresses]

    // Ensure the selected address from statement package is included
    if (statementPackageDetail?.address) {
      const selectedAddress = statementPackageDetail.address
      const addressExists = addressList.some(
        (addr) =>
          addr.addressNumber === selectedAddress.addressNumber &&
          addr.applicationId === selectedAddress.applicationId &&
          accountCodeToString(addr.accountCode) ===
            accountCodeToString(selectedAddress.accountCode),
      )

      if (!addressExists) {
        addressList.push(selectedAddress)
      }
    }

    return addressList
  }, [addresses, statementPackageDetail?.address])

  const accountCodeString = toAccountCodeString(accountCode)

  const showAddAddress = useCallback(() => {
    setAddressModalProps({
      accountCode,
      accountAddresses: addresses,
      onSave: async (addressForm: AddressFormSchema) => {
        const newAddressToSave = {
          ...formToAddress(addressForm),
          accountCode,
          effectiveDate: toUTCDateString(new Date()),
        }
        await createAddress(
          formToApiSchemas.address.parse({
            ...newAddressToSave,
            effectiveDate: toUTCDateString(new Date()),
          }),
        )
        form.setFieldValue(
          'addressCode',
          addressToAddressCode(newAddressToSave),
        )
        setCanAddNewAddress(false)
      },
    })
    addressModalState.show()
  }, [
    setAddressModalProps,
    accountCode,
    form,
    setCanAddNewAddress,
    addressModalState,
    createAddress,
  ])

  const showEditAddress = useCallback(
    (addressToEdit: AddressForm) => {
      setAddressModalProps({
        address: addressToEdit,
        accountCode,
        accountAddresses: addresses,
        onSave: (addressForm: AddressFormSchema) => {
          const editedAddress = {
            ...formToAddress(addressForm),
            accountCode,
            effectiveDate: toUTCDateString(new Date()),
          }
          form.setFieldValue('addressCode', addressToAddressCode(editedAddress))
        },
      })
      addressModalState.show()
    },
    [setAddressModalProps, accountCode, form, addressModalState],
  )

  return (
    <div className='w-full overflow-hidden overflow-y-scroll pt-12'>
      <div className='text-md ml-8 font-medium'>Edit statement package</div>
      <div className='ml-8 text-sm text-gray-600'>
        {(routeParams as StatementPackageRouteParams).packageNumber}
      </div>

      <form
        className='flex h-full flex-col'
        onSubmit={(event) => {
          event.preventDefault()
          event.stopPropagation()
          form.handleSubmit()
        }}
      >
        <div className='flex-1 px-8 py-6'>
          <InfoSection>
            <InfoSectionTitle>Statement package information</InfoSectionTitle>
            <InfoSectionDescription>
              Configure the delivery method and address for this statement
              package.
            </InfoSectionDescription>

            <div className='mt-6 grid grid-cols-2 gap-6'>
              <div className='flex flex-col'>
                <FormMonthPicker
                  name='effectiveDate'
                  label='Effective date'
                  required
                  onChange={(_effectiveMonth) => {
                    routeTo(
                      '/accounts/[effectiveMonth]/view/[accountCode]/statement-package/view/[packageNumber]',
                      routeParams as StatementPackageRouteParams,
                    )
                  }}
                />
              </div>
              <div></div>

              <FormInput
                name='statementPackageNumber'
                label='Package code'
                required
                readonly
              />

              <Select
                name='packageDelivery'
                label='Delivery method'
                placeholder='Select delivery method'
                required
                options={enumOptions(
                  formValidators.statementPackage.shape.packageDelivery,
                )}
                renderOption={(value) => PackageDeliveryLabels[value]}
                renderSelected={(value) => PackageDeliveryLabels[value]}
              />

              <div className='col-span-2 mb-4 flex flex-col gap-0'>
                <Select
                  name='addressCode'
                  label='Delivery address'
                  required
                  disabled={allAddresses.length === 0}
                  renderSelected={(value: AddressCode) => {
                    const address =
                      value.addressNumber > 0 ?
                        allAddresses.find(
                          (address) =>
                            addressCodeToString(value) ===
                            addressCodeToString(addressToAddressCode(address)),
                        )
                      : undefined
                    return address ?
                        <AddressOption
                          address={address}
                          editable={false}
                          fromKeyAccount={
                            accountCodeToString(value.accountCode) !==
                            accountCodeString
                          }
                        />
                      : <div className='text-zinc-400'>
                          Select delivery address
                        </div>
                  }}
                  renderErrors={false}
                >
                  {allAddresses.map((address) => {
                    const fromThisAccount =
                      accountCodeToString(address.accountCode) ===
                      accountCodeString
                    const addressCode = addressToAddressCode(address)
                    return (
                      <SelectOption
                        key={addressCodeToString(addressCode)}
                        value={addressCode}
                        focusClassNames='data-[focus]:bg-indigo-100'
                      >
                        <AddressOption
                          address={address}
                          editable={fromThisAccount}
                          onEdit={showEditAddress}
                          fromKeyAccount={!fromThisAccount}
                        />
                      </SelectOption>
                    )
                  })}
                </Select>
                <div className='mt-1 text-sm'>
                  {canAddNewAddress ?
                    <HeadlessButton
                      className='font-medium text-app-color-button-primary-bg'
                      onClick={() => showAddAddress()}
                    >
                      Add a new address
                    </HeadlessButton>
                  : <span>&nbsp;</span>}
                </div>
              </div>
            </div>
          </InfoSection>
        </div>

        <EditAccountFooter form={form} />
      </form>

      {/* Address Modal */}
      {addressModalProps && (
        <AddressModal {...addressModalProps} modalState={addressModalState} />
      )}
    </div>
  )
}
