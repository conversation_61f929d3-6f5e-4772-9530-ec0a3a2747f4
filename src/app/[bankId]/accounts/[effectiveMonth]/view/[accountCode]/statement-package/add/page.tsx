'use client'

import { useQuery } from '@tanstack/react-query'
import { data } from '@/lib/unions/Union'
import { useRoute } from '../routing'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRouter } from 'next/navigation'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { StatementPackageFormSchema } from '@/app/[bankId]/accounts/types'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { useExternalModalStateAndProps } from '@/components/Modal'
import { StatementPackageModal, StatementPackageModalInstanceProps } from '@/app/[bankId]/accounts/_components/StatementPackageModal'
import { useMemo, useEffect, useCallback, useState } from 'react'
import { AddressForm } from '@/api/formToApiSchema'

export default function AddStatementPackagePage() {
  const route = useRoute()!
  const routeParams = data(route).params
  const { account, accountCode, effectiveDate } = useAccountFromParams(routeParams)
  const router = useRouter()

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.hydratedStatementPackage),
  })

  const { data: addresses, isLoading: isLoadingAddresses } = useQuery({
    ...accountQueries('/getAddresses', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.address),
  })

  // Get existing statement package numbers to avoid duplicates
  const existingStatementPackageNumbers = useMemo(
    () => statementPackages?.map((pkg) => pkg.statementPackage.statementPackageNumber) || [],
    [statementPackages],
  )

  // State for managing addresses
  const [accountAddresses, setAccountAddresses] = useState<AddressForm[]>([])

  // Update addresses when data loads
  useEffect(() => {
    if (addresses) {
      setAccountAddresses(addresses)
    }
  }, [addresses])

  // Modal state management
  const [modalState, modalInstanceProps, setModalInstanceProps] =
    useExternalModalStateAndProps<StatementPackageModalInstanceProps>({})

  const handleSave = useCallback((value: StatementPackageFormSchema) => {
    // TODO: Implement save functionality
    console.log('Saving statement package:', value)

    // Navigate back to statement package list after save
    router.push(`/home/<USER>/${routeParams.effectiveMonth}/view/${routeParams.accountCode}/statement-package`)
  }, [router, routeParams])

  // Show the modal when component mounts
  useEffect(() => {
    if (!isLoading && !isLoadingAddresses && addresses) {
      setModalInstanceProps({
        existingStatementPackageNumbers,
        onSave: handleSave,
      })
      modalState.show()
    }
  }, [isLoading, isLoadingAddresses, addresses, existingStatementPackageNumbers, handleSave, setModalInstanceProps, modalState])

  // Handle modal close - navigate back to statement package list
  const handleModalClose = useCallback(() => {
    router.push(`/home/<USER>/${routeParams.effectiveMonth}/view/${routeParams.accountCode}/statement-package`)
  }, [router, routeParams])

  if (isLoading || isLoadingAddresses) {
    return <div>Loading...</div>
  }

  if (isError) {
    return <div className='text-red-600'>Error: {error?.message}</div>
  }

  if (!addresses || !account) {
    return <div>Data not found</div>
  }

  return (
    <>
      {modalInstanceProps && (
        <StatementPackageModal
          modalState={{
            ...modalState,
            close: handleModalClose,
          }}
          {...modalInstanceProps}
          accountCode={accountCode}
          shortName={account.shortName}
          accountAddresses={accountAddresses}
          setAccountAddresses={setAccountAddresses}
          keyAccountAddresses={[]} // No key account addresses for this context
          subAccounts={[]} // No sub accounts for this context
        />
      )}
    </>
  )
}