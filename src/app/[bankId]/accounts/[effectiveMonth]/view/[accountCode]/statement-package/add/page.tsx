'use client'

import { useQuery } from '@tanstack/react-query'
import { data } from '@/lib/unions/Union'
import { useRoute } from '../routing'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRouter } from 'next/navigation'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { StatementPackageFormSchema } from '@/app/[bankId]/accounts/types'
import { AddStatementPackageForm } from './AddStatementPackageForm'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function AddStatementPackagePage() {
  const route = useRoute()!
  const routeParams = data(route).params
  const { accountCode, effectiveDate } = useAccountFromParams(routeParams)
  const router = useRouter()

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.hydratedStatementPackage),
  })

  const { data: addresses, isLoading: isLoadingAddresses } = useQuery({
    ...accountQueries('/getAddresses', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.address),
  })

  const handleSave = (value: StatementPackageFormSchema) => {
    // TODO: Implement save functionality
    console.log('Saving statement package:', value)

    // Navigate back to statement package list after save
    router.push(`/home/<USER>/${routeParams.effectiveMonth}/view/${routeParams.accountCode}/statement-package`)
  }

  if (isLoading || isLoadingAddresses) {
    return <div>Loading...</div>
  }

  if (isError) {
    return <div className='text-red-600'>Error: {error?.message}</div>
  }

  if (!addresses) {
    return <div>Addresses not found</div>
  }

  return (
    <AddStatementPackageForm
      addresses={addresses}
      statementPackages={statementPackages || []}
      onSave={handleSave}
    />
  )
}