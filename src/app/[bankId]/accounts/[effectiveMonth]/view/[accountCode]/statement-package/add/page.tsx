'use client'

import { useQuery } from '@tanstack/react-query'
import { data } from '@/lib/unions/Union'
import { useRoute } from '../routing'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRouter } from 'next/navigation'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { StatementPackageFormSchema } from '@/app/[bankId]/accounts/types'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { useMemo, useState, useEffect, useCallback } from 'react'
import { AddressForm } from '@/api/formToApiSchema'
import { StatementPackageModal, StatementPackageModalInstanceProps } from '@/app/[bankId]/accounts/_components/StatementPackageModal'
import { useExternalModalStateAndProps } from '@/components/Modal'

export default function AddStatementPackagePage() {
  const route = useRoute()!
  const routeParams = data(route).params
  const { account, accountCode, effectiveDate } = useAccountFromParams(routeParams)
  const router = useRouter()

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.hydratedStatementPackage),
  })

  const { data: addresses, isLoading: isLoadingAddresses } = useQuery({
    ...accountQueries('/getAddresses', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.address),
  })

  // Get existing statement package numbers to avoid duplicates
  const existingStatementPackageNumbers = useMemo(
    () => statementPackages?.map((pkg) => pkg.statementPackage.statementPackageNumber) || [],
    [statementPackages],
  )

  // State for managing addresses
  const [accountAddresses, setAccountAddresses] = useState<AddressForm[]>([])

  // Update addresses when data loads
  useEffect(() => {
    if (addresses) {
      setAccountAddresses(addresses)
    }
  }, [addresses])

  // Modal state management - but we'll show it immediately as a full page
  const [modalState, modalInstanceProps, setModalInstanceProps] =
    useExternalModalStateAndProps<StatementPackageModalInstanceProps>({})

  const handleSave = useCallback((value: StatementPackageFormSchema) => {
    // TODO: Implement save functionality
    console.log('Saving statement package:', value)

    // Navigate back to statement package list after save
    router.push(`/home/<USER>/${routeParams.effectiveMonth}/view/${routeParams.accountCode}/statement-package`)
  }, [router, routeParams])

  const handleCancel = useCallback(() => {
    // Navigate back to statement package list
    router.push(`/home/<USER>/${routeParams.effectiveMonth}/view/${routeParams.accountCode}/statement-package`)
  }, [router, routeParams])

  // Show the modal when component mounts
  useEffect(() => {
    if (!isLoading && !isLoadingAddresses && addresses && account) {
      setModalInstanceProps({
        existingStatementPackageNumbers,
        onSave: handleSave,
      })
      modalState.show()
    }
  }, [isLoading, isLoadingAddresses, addresses, account, existingStatementPackageNumbers, handleSave, setModalInstanceProps, modalState])

  // Override modal close to navigate back
  const modalStateWithNavigation = useMemo(() => ({
    ...modalState,
    close: handleCancel,
  }), [modalState, handleCancel])

  if (isLoading || isLoadingAddresses) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>Loading...</div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className='text-red-600'>Error: {error?.message}</div>
      </div>
    )
  }

  if (!addresses || !account) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>Data not found</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {modalInstanceProps && (
        <StatementPackageModal
          modalState={modalStateWithNavigation}
          {...modalInstanceProps}
          accountCode={accountCode}
          shortName={account.shortName}
          accountAddresses={accountAddresses}
          setAccountAddresses={setAccountAddresses}
          keyAccountAddresses={[]} // No key account addresses for this context
          subAccounts={[]} // No sub accounts for this context
        />
      )}
    </div>
  )
}