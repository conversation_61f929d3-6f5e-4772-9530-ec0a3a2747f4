'use client'

import { useQuery } from '@tanstack/react-query'
import { data } from '@/lib/unions/Union'
import { useRoute } from '../routing'
import { routeTo as parentRouteTo } from '../../routing'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { StatementPackageFormSchema, BaseRouteParams } from '@/app/[bankId]/accounts/types'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { useMemo, useState, useEffect, useCallback, useRef } from 'react'
import { AddressForm } from '@/api/formToApiSchema'
import { StatementPackageModal, StatementPackageModalInstanceProps } from '@/app/[bankId]/accounts/_components/StatementPackageModal'
import { useExternalModalStateAndProps } from '@/components/Modal'

export default function AddStatementPackagePage() {
  const route = useRoute()!
  const routeParams = data(route).params
  const { account, accountCode, effectiveDate } = useAccountFromParams(routeParams)

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.hydratedStatementPackage),
  })

  const { data: addresses, isLoading: isLoadingAddresses } = useQuery({
    ...accountQueries('/getAddresses', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.address),
  })

  const existingStatementPackageNumbers = useMemo(
    () => statementPackages?.map((pkg) => pkg.statementPackage.statementPackageNumber) || [],
    [statementPackages],
  )

  const [accountAddresses, setAccountAddresses] = useState<AddressForm[]>([])

  
  useEffect(() => {
    if (addresses) {
      setAccountAddresses(addresses)
    }
  }, [addresses])

  const handleSave = useCallback((value: StatementPackageFormSchema) => {
    parentRouteTo(
      '/accounts/[effectiveMonth]/view/[accountCode]/statement-package',
      routeParams as BaseRouteParams,
    )
  }, [routeParams.bankId, routeParams.effectiveMonth, routeParams.accountCode])

  const handleCancel = useCallback(() => {
    // Navigate back to statement package list
    parentRouteTo(
      '/accounts/[effectiveMonth]/view/[accountCode]/statement-package',
      routeParams as BaseRouteParams,
    )
  }, [routeParams.bankId, routeParams.effectiveMonth, routeParams.accountCode])
  const [modalState, modalInstanceProps, setModalInstanceProps] =
    useExternalModalStateAndProps<StatementPackageModalInstanceProps>({})

  const modalInitialized = useRef(false)
  useEffect(() => {
    if (!isLoading && !isLoadingAddresses && addresses && account && !modalInitialized.current) {
      setModalInstanceProps({
        existingStatementPackageNumbers,
        onSave: handleSave,
      })
      modalState.show()
      modalInitialized.current = true
    }
  }, [isLoading, isLoadingAddresses, addresses, account, existingStatementPackageNumbers, handleSave, setModalInstanceProps, modalState])

  // Override modal close to navigate back
  const modalStateWithNavigation = useMemo(() => ({
    ...modalState,
    close: handleCancel,
  }), [modalState, handleCancel])

  if (isLoading || isLoadingAddresses) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>Loading...</div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className='text-red-600'>Error: {error?.message}</div>
      </div>
    )
  }

  if (!addresses || !account) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>Data not found</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {modalInstanceProps && (
        <StatementPackageModal
          modalState={modalStateWithNavigation}
          {...modalInstanceProps}
          accountCode={accountCode}
          shortName={account.shortName}
          accountAddresses={accountAddresses}
          setAccountAddresses={setAccountAddresses}
          keyAccountAddresses={[]} // No key account addresses for this context
          subAccounts={[]} // No sub accounts for this context
        />
      )}
    </div>
  )
}
