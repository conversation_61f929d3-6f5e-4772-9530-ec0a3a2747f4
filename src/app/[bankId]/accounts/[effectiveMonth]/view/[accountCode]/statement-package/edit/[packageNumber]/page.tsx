'use client'

import { useQuery } from '@tanstack/react-query'
import { data } from '@/lib/unions/Union'
import { routeTo, useRoute } from '../../routing'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { StatementPackageFormSchema, StatementPackageRouteParams } from '@/app/[bankId]/accounts/types'
import { EditStatementPackageForm } from './EditStatementPackageForm'
import { listSelector } from '@/api/selectors'
import { apiToFormSchemas } from '@/api/apiToFormSchema'

export default function EditStatementPackagePage() {
  const route = useRoute()!
  const routeParams = data(route).params
  const { accountCode, effectiveDate } = useAccountFromParams(routeParams)

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery({
    ...accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.hydratedStatementPackage),
  })

  const { data: addresses, isLoading: isLoadingAddresses } = useQuery({
    ...accountQueries('/getAddresses', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
    select: listSelector(apiToFormSchemas.address),
  })

  // Find the specific statement package
  const statementPackageDetail = statementPackages?.find(
    (item) =>
      item.statementPackage.statementPackageNumber.toString() ===
      (routeParams as StatementPackageRouteParams).packageNumber,
  )

  const handleSave = (_value: StatementPackageFormSchema) => {
    // TODO: Implement save functionality

    // Navigate back to view page after save
    routeTo(
      '/accounts/[effectiveMonth]/view/[accountCode]/statement-package/view/[packageNumber]',
      routeParams as StatementPackageRouteParams,
    )
  }

  if (isLoading || isLoadingAddresses) {
    return <div>Loading...</div>
  }

  if (isError) {
    return <div className='text-red-600'>Error: {error?.message}</div>
  }

  if (!statementPackages || !statementPackageDetail || !addresses) {
    return <div>Statement package or addresses not found</div>
  }

  return (
    <EditStatementPackageForm
      statementPackageDetail={statementPackageDetail}
      addresses={addresses}
      statementPackages={statementPackages}
      onSave={handleSave}
    />
  )
}
