'use client'

import { useForm } from '@tanstack/react-form'
import { useMemo } from 'react'
import { useRouter } from 'next/navigation'
import {
  StatementPackageFormSchema,
  StatementPackageFormZodSchema,
  StatementPackageFormDefaults,
  AddressCode,
} from '@/app/[bankId]/accounts/types'
import { HydratedStatementPackageForm, AddressForm } from '@/api/formToApiSchema'
import { useFormMonthPicker } from '@/components/Form/useFormMonthPicker'
import { useFormTextInput } from '@/components/Form/useFormTextInput'
import { useFormSelect } from '@/components/Form/useFormSelect'
import { Button } from '@/components/Button'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { enumOptions } from '@/lib/validation/enumOptions'
import { formValidators } from '@/api/formToApiSchema'
import { PackageDeliveryLabels, PackageTypeLabels } from '@/app/[bankId]/accounts/types'
import { addressToAddressCode, formatAddress, formatAddressNumber } from '@/app/[bankId]/accounts/accountHelpers'
import { SelectOption } from '@/components/Input/Select'

export interface AddStatementPackageFormProps {
  addresses: AddressForm[]
  statementPackages: HydratedStatementPackageForm[]
  onSave: (value: StatementPackageFormSchema) => void
}

export function AddStatementPackageForm({
  addresses,
  statementPackages,
  onSave,
}: AddStatementPackageFormProps) {
  const router = useRouter()
  // Get existing statement package numbers to avoid duplicates
  const existingStatementPackageNumbers = useMemo(
    () =>
      statementPackages.map(
        (pkg) => pkg.statementPackage.statementPackageNumber,
      ),
    [statementPackages],
  )

  // Find next available statement package number
  const nextAvailableNumber = useMemo(() => {
    let number = 1
    while (existingStatementPackageNumbers.includes(number)) {
      number++
    }
    return number
  }, [existingStatementPackageNumbers])

  const initialValues: StatementPackageFormSchema = {
    ...StatementPackageFormDefaults,
    statementPackageNumber: nextAvailableNumber,
    existingStatementPackageNumbers,
  }

  // Create form with proper defaults
  const form = useForm<StatementPackageFormSchema>({
    defaultValues: initialValues,
    validators: {
      onSubmit: StatementPackageFormZodSchema,
    },
    onSubmit: async ({ value }) => {
      onSave(value)
    },
  })

  const FormMonthPicker = useFormMonthPicker<StatementPackageFormSchema>({
    form,
  })
  const FormInput = useFormTextInput<StatementPackageFormSchema>({ form })
  const Select = useFormSelect<StatementPackageFormSchema>({ form })

  return (
    <div className='flex min-h-screen flex-col'>
      <div className='flex-1 overflow-y-auto p-8'>
        <div className='mb-6'>
          <h1 className='text-2xl font-semibold text-gray-900'>
            Add Statement Package
          </h1>
          <p className='mt-1 text-sm text-gray-600'>
            Create a new statement package for this account.
          </p>
        </div>

        <form
          className='space-y-8'
          onSubmit={async (event) => {
            event.preventDefault()
            event.stopPropagation()
            await form.handleSubmit()
          }}
        >
          <InfoSection>
            <InfoSectionTitle>Package Information</InfoSectionTitle>
            <InfoSectionDescription>
              Configure the basic settings for this statement package.
            </InfoSectionDescription>

            <div className='grid grid-cols-2 gap-6'>
              <FormMonthPicker
                name='effectiveDate'
                label='Effective date'
                required
              />

              <FormInput
                name='statementPackageNumber'
                label='Package code'
                required
                type='number'
              />

              <Select
                name='packageDelivery'
                label='Delivery method'
                required
                options={enumOptions(
                  formValidators.statementPackage.shape.packageDelivery,
                )}
                renderOption={(value) => PackageDeliveryLabels[value]}
                renderSelected={(value) => PackageDeliveryLabels[value]}
              />

              <Select
                name='addressCode'
                label='Delivery address'
                required
                renderSelected={(value: AddressCode) => {
                  const address = addresses.find(
                    (addr) =>
                      addr.applicationId === value.applicationId &&
                      addr.addressNumber === value.addressNumber &&
                      addr.accountCode.accountNumber === value.accountCode.accountNumber &&
                      addr.accountCode.applicationId === value.accountCode.applicationId &&
                      addr.accountCode.bankNumber === value.accountCode.bankNumber
                  )
                  return address ?
                    formatAddress(address) :
                    'Select delivery address'
                }}
                renderErrors={false}
              >
                {addresses.map((address) => {
                  const addressCode = addressToAddressCode(address)
                  return (
                    <SelectOption
                      key={`${address.applicationId}-${address.addressNumber}`}
                      value={addressCode}
                      focusClassNames='data-[focus]:bg-indigo-100'
                    >
                      <div className='flex flex-col'>
                        <span>{formatAddress(address)}</span>
                        <span className='text-gray-500'>
                          {address.applicationId} - {formatAddressNumber(address.addressNumber)}
                        </span>
                      </div>
                    </SelectOption>
                  )
                })}
              </Select>

              <div className='col-span-2'>
                <Select
                  name='packageType'
                  label='Include accounts'
                  required
                  options={enumOptions(
                    formValidators.statementPackage.shape.packageType,
                  )}
                  renderOption={(value) => PackageTypeLabels[value]}
                  renderSelected={(value) => PackageTypeLabels[value]}
                  onChange={(value, form) => {
                    if (value !== 'SELECTED_ACCOUNTS') {
                      // clear selected accounts
                      form.setFieldValue('selectedAccounts', [])
                    }
                  }}
                />
              </div>
            </div>
          </InfoSection>

          <div className='flex justify-end gap-4 border-t bg-white px-6 py-4'>
            <Button
              type='button'
              className='btn w-32'
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button type='submit' className='btn-primary w-32'>
              Save
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
