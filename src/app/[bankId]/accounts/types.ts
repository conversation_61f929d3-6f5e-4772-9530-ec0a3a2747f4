import { apiToFormSchemas } from '@/api/apiToFormSchema'
import {
  formToApiSchemas,
  formValidators,
  AddressForm,
  AddressCodeForm,
  AccountCodeForm,
  HydratedStatementPackageForm,
  ServicePriceForm,
} from '@/api/formToApiSchema'
import {
  accountNumber,
  costCenter,
  threeDigitNumber,
  zipCode,
  requiredString,
  shortName,
} from '@/api/zodSchemas'
import { FormSchemaType } from '@/app/types'
import { toUTCDateString } from '@/lib/date'
import { EnumLabels } from '@/lib/validation/enumOptions'
import { ColumnDef } from '@tanstack/react-table'
import { z } from 'zod'
import { FormApi } from '@tanstack/react-form'
import { AccountTypeOverrideForm } from '@/api/formToApiSchema'

// re-exported with new names to reduce diffs in other files
export type AccountCode = AccountCodeForm
export type AddressCode = AddressCodeForm
export type Address = AddressForm
export type HydratedStatementPackage = HydratedStatementPackageForm

export type PricingOptionsItem = {
  name: string
}

export const AccountStatusLabels: EnumLabels<
  typeof formValidators.account.shape.accountStatus
> = {
  Z: 'Open, zero balance',
  B: 'Closed by bank',
  C: 'Closed by customer',
  P: 'Closed by processing',
  X: 'Removed from analysis',
}

export const DepositCategoryLabels: EnumLabels<
  typeof formValidators.account.shape.depositCategory
> = {
  A: 'Automatic Transfer',
  C: 'Certificate of Deposit',
  D: 'Demand Deposit',
  L: 'Line of Credit',
  M: 'Money Market',
  N: 'Negotiable Order of Withdrawal (NOW) Account',
  R: 'Repurchase Agreement',
  S: 'Savings',
  P: 'Private Money',
  T: 'Time Deposit',
}

export const AccountZodSchema = z.object({
  ...formValidators.accountWithKey.shape,
  accountNumber,
  shortName,
  // QUICK AND DIRTY HACK after https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/pull-requests/264/overview
  // removed keyAccountCode from Account. Might be cleaned up with https://jira.fis.dev/browse/REVCON-53.
  keyAccountCode: formValidators.accountCode.nullable(),
})

export type Account = z.infer<typeof AccountZodSchema>

export type AccountListItem = Required<
  Pick<
    Account,
    | 'applicationId'
    | 'bankNumber'
    | 'accountNumber'
    | 'shortName'
    | 'isKeyAccount'
  >
> &
  Partial<
    Omit<
      Account,
      'applicationId' | 'accountNumber' | 'shortName' | 'isKeyAccount'
    >
  > & {
    children?: AccountListItem[]
  }

export type AccountSearchItem = Pick<
  Account,
  'bankNumber' | 'accountNumber' | 'shortName' | 'isKeyAccount'
> &
  Partial<Pick<Account, 'applicationId'>>

// Bound recursion depth of AccountListItem to avoid infinite depth type errors in Tanstack Form.
// We are bounding up to seven levels of recursion as that is the maximum recursion depth returned
// by the backend.
type AccountListItemLevel7 = Omit<AccountListItem, 'children'> & {
  children?: undefined
}
type AccountListItemLevel6 = Omit<AccountListItem, 'children'> & {
  children?: AccountListItemLevel7[]
}
type AccountListItemLevel5 = Omit<AccountListItem, 'children'> & {
  children?: AccountListItemLevel6[]
}
type AccountListItemLevel4 = Omit<AccountListItem, 'children'> & {
  children?: AccountListItemLevel5[]
}
type AccountListItemLevel3 = Omit<AccountListItem, 'children'> & {
  children?: AccountListItemLevel4[]
}
type AccountListItemLevel2 = Omit<AccountListItem, 'children'> & {
  children?: AccountListItemLevel3[]
}
type AccountListItemLevel1 = Omit<AccountListItem, 'children'> & {
  children?: AccountListItemLevel2[]
}
export type BoundedAccountListItem = AccountListItemLevel1

// Define the Zod schema for AccountListItem without children
export const BaseAccountListItemZodSchema = AccountZodSchema.pick({
  applicationId: true,
  accountNumber: true,
  bankNumber: true,
  shortName: true,
  isKeyAccount: true,
}).extend(
  AccountZodSchema.pick({
    analysisAccountTypeCode: true,
    openDate: true,
    currencyCode: true,
  }).partial().shape,
)

// Bound recursion depth of AccountListItem to avoid infinite depth type errors in Tanstack Form.
// We are bounding up to seven levels of recursion as that is the maximum recursion depth returned
// by the backend.
const AccountListItemZodSchemaLevel7 = BaseAccountListItemZodSchema.extend({
  children: z.undefined().optional(),
})

const AccountListItemZodSchemaLevel6 = BaseAccountListItemZodSchema.extend({
  children: z.array(AccountListItemZodSchemaLevel7).optional(),
})

const AccountListItemZodSchemaLevel5 = BaseAccountListItemZodSchema.extend({
  children: z.array(AccountListItemZodSchemaLevel6).optional(),
})

const AccountListItemZodSchemaLevel4 = BaseAccountListItemZodSchema.extend({
  children: z.array(AccountListItemZodSchemaLevel5).optional(),
})

const AccountListItemZodSchemaLevel3 = BaseAccountListItemZodSchema.extend({
  children: z.array(AccountListItemZodSchemaLevel4).optional(),
})

const AccountListItemZodSchemaLevel2 = BaseAccountListItemZodSchema.extend({
  children: z.array(AccountListItemZodSchemaLevel3).optional(),
})

const AccountListItemZodSchemaLevel1 = BaseAccountListItemZodSchema.extend({
  children: z.array(AccountListItemZodSchemaLevel2).optional(),
})

const BoundedAccountListItemZodSchema = AccountListItemZodSchemaLevel1

// account information

export const AccountInformationZodSchema = AccountZodSchema.pick({
  accountNumber: true,
  applicationId: true,
  bankNumber: true,
  effectiveDate: true,
  openDate: true,
  shortName: true,
  isKeyAccount: true,
  keyAccountCode: true,
}).extend({
  useNextAvailableAccountNumber: z.boolean(),
})

export type AccountInformationFormSchema = z.infer<
  typeof AccountInformationZodSchema
>

export const EditAccountInformationZodSchema = AccountZodSchema.pick({
  effectiveDate: true,
  shortName: true,
})

export type EditAccountInformationFormSchema = z.infer<
  typeof EditAccountInformationZodSchema
>

// key account, demographics, user fields, & pricing options

export type UserFieldListItem = {
  configuration: z.infer<typeof apiToFormSchemas.hydratedUserFieldConfiguration>
  selection: z.infer<typeof apiToFormSchemas.userFieldSelection>
}

const UserFieldListItemZodSchema = z.object({
  configuration: formValidators.hydratedUserFieldConfiguration,
  selection: formValidators.userFieldSelection,
})

export const DemographicsAndPricingZodSchema = AccountZodSchema.pick({
  // key account
  keyAccountCode: true,
  // demographics
  analysisAccountTypeCode: true,
  costCenter: true,
  branchCode: true,
  currencyCode: true,
  depositAccountTypeCode: true,
  depositCategory: true,
  primaryOfficerCode: true,
  secondaryOfficerCode: true,
  treasuryOfficerCode: true,
  // pricing
  customerSpecificPricingIndicator: true,
}).extend({
  // user fields
  userFields: z.array(UserFieldListItemZodSchema),
})

export type DemographicsAndPricingFormSchema = z.infer<
  typeof DemographicsAndPricingZodSchema
>

export const EditDemographicsAndPricingZodSchema =
  DemographicsAndPricingZodSchema.extend({
    effectiveDate: AccountZodSchema.shape.effectiveDate,
  })

export type EditDemographicsAndPricingFormSchema = z.infer<
  typeof EditDemographicsAndPricingZodSchema
>

export type EditDemographicsAndPricingFormSchemaType =
  FormSchemaType<EditDemographicsAndPricingFormSchema>

// settlement & processing

/**
 * @deprecated TODO use `formValidators.accountTypeOverride` instead
 */
const AccountTypeOverrideZodSchema = z.object({
  accountCode: formValidators.accountCode,
  effectiveDate: z.string().date(),
  analysisResultOptionsPlanCode: z.string().optional(),
  analysisResultOptionsPlanCodeExpiry: z.coerce.string().date().optional(),
  balanceRequirementDefinitionCode: z.string().optional(),
  balanceRequirementDefinitionCodeExpiry: z.coerce.string().date().optional(),
  earningsCreditDefinitionCode: z.string().optional(),
  earningsCreditDefinitionCodeExpiry: z.coerce.string().date().optional(),
  interestRequirementDefinitionCode: z.string().optional(),
  interestRequirementDefinitionCodeExpiry: z.coerce.string().date().optional(),
  investableBalanceDefinitionCode: z.string().optional(),
  investableBalanceDefinitionCodeExpiry: z.coerce.string().date().optional(),
  reserveRequirementDefinitionCode: z.string().optional(),
  reserveRequirementDefinitionCodeExpiry: z.coerce.string().date().optional(),
  settlementCyclePlanCode: z.string().optional(),
  settlementCyclePlanCodeExpiry: z.coerce.string().date().optional(),
  statementCyclePlanCode: z.string().optional(),
  statementCyclePlanCodeExpiry: z.coerce.string().date().optional(),
  statementFormatPlanCode: z.string().optional(),
  statementFormatPlanCodeExpiry: z.coerce.string().date().optional(),
  statementMessagePlanCode: z.string().optional(),
  statementMessagePlanCodeExpiry: z.coerce.string().date().optional(),
  isOverrideAsSettlementAccount: z.boolean(),
})

/**
 * @deprecated TODO use `AccountTypeOverrideForm` from `formToApiSchemas` instead
 */
export type AccountTypeOverride = z.infer<typeof AccountTypeOverrideZodSchema>

// statement packages & addresses

export const PackageDeliveryLabels: EnumLabels<
  typeof formValidators.statementPackage.shape.packageDelivery
> = {
  ELECTRONIC: 'Electronic',
  ELECTRONIC_AND_PRINT: 'Electronic and print',
  PRINT: 'Print',
  NO_STATEMENT: 'No statement',
}

export const PackageTypeLabels: EnumLabels<
  typeof formValidators.statementPackage.shape.packageType
> = {
  ALL_ACCOUNTS: 'All accounts',
  COMPOSITE_ACCOUNTS: 'Composite accounts',
  DEPOSIT_ACCOUNTS: 'Deposit accounts',
  SELECTED_ACCOUNTS: 'Selected accounts',
}

// composite account

export const CompositeAccountZodSchema = z.object({
  // Step 1 - Add Account Information
  accountInfo: AccountInformationZodSchema,
  // Step 2 - Select Child Accounts
  subAccounts: z.array(BoundedAccountListItemZodSchema),
  // Step 3 Select Key Account & Configure Demographics and Pricing
  skipKeyAccountSelection: z.boolean(),
  demographics: DemographicsAndPricingZodSchema,
  // Step 4 - Configure Settlement and Processing
  accountTypeOverride: AccountTypeOverrideZodSchema,
  // Step 5 - Configure Statement Packages
  statementPackages: z.array(
    formValidators.statementPackageCreateUpdateRequest,
  ),
  addresses: z.array(formValidators.address),
})

export type CompositeAccountFormSchema = z.infer<
  typeof CompositeAccountZodSchema
>

export type CreateCompositeAccountFormSchemaType =
  FormSchemaType<CompositeAccountFormSchema>

export const CompositeAccountFormDefaults: CompositeAccountFormSchema = {
  // Step 1 - Add Account Information
  accountInfo: {
    accountNumber: '',
    applicationId: 'C',
    bankNumber: '',
    effectiveDate: toUTCDateString(new Date()),
    openDate: '',
    shortName: '',
    useNextAvailableAccountNumber: false,
    isKeyAccount: null,
    keyAccountCode: null,
  },
  // Step 2 - Select Child Accounts
  subAccounts: [],
  // Step 3 - Select Key Account & Configure Demographics and Pricing
  skipKeyAccountSelection: true,
  demographics: {
    // key account
    keyAccountCode: null,
    // demographics
    analysisAccountTypeCode: '',
    costCenter: '',
    branchCode: '',
    currencyCode: 'USD',
    depositAccountTypeCode: null,
    depositCategory: null,
    primaryOfficerCode: '',
    secondaryOfficerCode: null,
    treasuryOfficerCode: null,
    // user fields
    userFields: [],
    // pricing
    customerSpecificPricingIndicator: false,
  },
  // Step 4 - Configure Settlement and Processing
  accountTypeOverride: {
    accountCode: {
      applicationId: 'C',
      accountNumber: '',
      bankNumber: '',
    },
    effectiveDate: toUTCDateString(new Date()),
    isOverrideAsSettlementAccount: false,
  },
  // Statement Packages & Addresses
  statementPackages: [],
  addresses: [],
}

export enum CreateCompositeAccountStepID {
  step1_AddAccountInformation,
  step2_SelectChildAccounts,
  step3_SelectKeyAccount,
  step3_ConfigureDemographicsAndPricing,
  step4_ConfigureSettlementAndProcessing,
  step5_ConfigureStatementPackages,
}

export type NextAvailableAccountNumber = {
  nextValidAccountNumber: string
}

export const StatementPackageFormZodSchema = z
  .object({
    ...formValidators.statementPackage.omit({ accountCode: true }).shape,
    statementPackageNumber: threeDigitNumber,
    addressCode: formValidators.addressCode.refine((value) => !!value, {
      message: 'Please select delivery address',
    }),
    selectedAccounts: z.array(formValidators.accountCode),
    existingStatementPackageNumbers: z.array(threeDigitNumber),
  })
  .refine(
    (value) =>
      !value.existingStatementPackageNumbers.includes(
        value.statementPackageNumber,
      ),
    {
      message: 'Package code already exists',
      path: ['statementPackageNumber'],
    },
  )

export type StatementPackageFormSchema = z.infer<
  typeof StatementPackageFormZodSchema
>

export const StatementPackageFormDefaults: StatementPackageFormSchema = {
  effectiveDate: toUTCDateString(new Date()),
  statementPackageNumber: 1,
  packageDelivery: 'ELECTRONIC_AND_PRINT',
  addressCode: null as unknown as AddressCode,
  packageType: 'ALL_ACCOUNTS',
  selectedAccounts: [],
  existingStatementPackageNumbers: [],
}

const ReadonlyStateCodes = [
  'AL',
  'AK',
  'AZ',
  'AR',
  'CA',
  'CO',
  'CT',
  'DE',
  'FL',
  'GA',
  'HI',
  'ID',
  'IL',
  'IN',
  'IA',
  'KS',
  'KY',
  'LA',
  'ME',
  'MD',
  'MA',
  'MI',
  'MN',
  'MS',
  'MO',
  'MT',
  'NE',
  'NV',
  'NH',
  'NJ',
  'NM',
  'NY',
  'NC',
  'ND',
  'OH',
  'OK',
  'OR',
  'PA',
  'RI',
  'SC',
  'SD',
  'TN',
  'TX',
  'UT',
  'VT',
  'VA',
  'WA',
  'WV',
  'WI',
  'WY',
] as const
export const StateCodeChoices = [...ReadonlyStateCodes]

const StateCode = z.enum(ReadonlyStateCodes, { message: 'Required' })
export type StateCode = z.infer<typeof StateCode>

export const AddressFormZodSchema = z.object({
  addressNumber: threeDigitNumber,
  recipient: requiredString,
  streetAddress: requiredString,
  streetAddress2: z.preprocess(
    (val) => (val === null ? '' : val),
    z.string().optional(),
  ) as unknown as z.ZodString,
  city: requiredString,
  zipCode,
  state: StateCode,
})

export type AddressFormSchema = z.infer<typeof AddressFormZodSchema>

export const AddressFormSchemaDefaults: AddressFormSchema = {
  addressNumber: 1,
  recipient: '',
  streetAddress: '',
  streetAddress2: '',
  city: '',
  zipCode: '',
  state: '' as StateCode,
}

export const CompositeAccountCreateRequestFormToApiSchema =
  formToApiSchemas.compositeAccountCreateRequest.extend({
    keyChildAccountCode:
      formToApiSchemas.compositeAccountCreateRequest.shape.keyChildAccountCode
        .nullable()
        .transform((value) => (value === null ? undefined : value)),
    // TODO ideally we shouldn't need to override these
    userFieldSelections: z.array(formToApiSchemas.userFieldSelection),
    accountTypeOverride: AccountTypeOverrideZodSchema,
  })

const SelectAccountZodSchema = CompositeAccountZodSchema.pick({
  accountInfo: true,
  subAccounts: true,
  demographics: true,
  skipKeyAccountSelection: true,
})
export type SelectAccountFormSchema = z.infer<typeof SelectAccountZodSchema>

export type SelectAccountFormSchemaType = FormSchemaType<
  z.infer<typeof SelectAccountZodSchema>
>

export interface SettlementOption {
  label: string
  value: boolean
}

export type ServiceOverrideRow = Required<
  Pick<
    ServicePriceForm,
    'serviceCode' | 'priceType' | 'priceValue' | 'disposition' | 'currency'
  >
> &
  Partial<
    Omit<
      ServicePriceForm,
      'serviceCode' | 'priceType' | 'priceValue' | 'disposition' | 'currency'
    >
  > & {
    serviceDescription?: string
    serviceType?: string
    startDate?: string
    expirationDate?: string
    serviceCategory?: {
      name: string
    }
    servicesInServiceSet?: Array<{
      code: string
      description: string
      serviceType: string
    }>
    children?: ServiceOverrideRow[]
  }

export type ServiceOverrideColumnDef = ColumnDef<ServiceOverrideRow>

// Common base types for settlement and processing options
export interface SettlementProcessingOptionItemWithId {
  id: string
  name: string
  field: string
  override: boolean
  planCode?: string
  plan: string
  expiry?: string
}

export interface SettlementAndProcessingOptionItem {
  field: string
  name: string
}

export interface SettlementAndProcessingOptionExpirationDateProps {
  expiry?: string
  isVisible: boolean
  onCheckboxChange: (checked: boolean) => void
  onMonthPickerChange: (date: string) => void
  disabled?: boolean
}

export interface SettlementAndProcessingSelectOptionProps {
  field: string
  value: string
  optionsMap: Map<string, string>
  disabled?: boolean
  defaultValue?: string
  onChange: (value: string) => void
}

// Settlement-specific types extending the common base
export interface SettlementOptionItem {
  field: 'analysisResultOptionsPlanCode' | 'settlementCyclePlanCode'
  name: string
}

export const settlementOptions: SettlementOptionItem[] = [
  {
    field: 'analysisResultOptionsPlanCode',
    name: 'Analysis result options',
  },
  {
    field: 'settlementCyclePlanCode',
    name: 'Settlement cycle',
  },
]

export type SettlementOptionsColumnDef = ColumnDef<SettlementOptionItem>

// Settlement option with extended data (includes id, override, planCode, etc.)
export type SettlementOptionWithData = SettlementProcessingOptionItemWithId & {
  field: SettlementOptionItem['field']
}

// Legacy type alias for backward compatibility
export interface SetSettlementOptionsProps {
  form: FormApi<AccountTypeOverrideForm, undefined>
  effectiveDate: string
  accountCode: string
}

export interface ProcessingOptionItem {
  field:
    | 'earningsCreditDefinitionCode'
    | 'investableBalanceDefinitionCode'
    | 'balanceRequirementDefinitionCode'
    | 'statementCyclePlanCode'
    | 'statementFormatPlanCode'
  name: string
}

export type ProcessingOptionsColumnDef = ColumnDef<ProcessingOptionItem>

// Processing option with extended data (includes id, override, planCode, etc.)
export type ProcessingOptionWithData = SettlementProcessingOptionItemWithId & {
  field: ProcessingOptionItem['field']
}

export interface SetProcessingOptionsProps {
  form: FormApi<AccountTypeOverrideForm, undefined>
  effectiveDate: string
  accountCode: string
}

export const processingOptions: ProcessingOptionItem[] = [
  {
    field: 'earningsCreditDefinitionCode',
    name: 'Earnings credit',
  },
  {
    field: 'investableBalanceDefinitionCode',
    name: 'Investable balance',
  },
  {
    field: 'balanceRequirementDefinitionCode',
    name: 'Required balance',
  },
  {
    field: 'statementCyclePlanCode',
    name: 'Statement cycle',
  },
  {
    field: 'statementFormatPlanCode',
    name: 'Statement format',
  },
]

export type OverrideTableRow = {
  override: boolean
  pricingAttribute: string
  priceType: string
  isSubRow?: boolean
}

export interface EditStatementPackageFormProps {
  statementPackageDetail: HydratedStatementPackageForm
  addresses: AddressForm[]
  statementPackages: HydratedStatementPackageForm[]
  onSave: (value: StatementPackageFormSchema) => void
}

// Route parameter types
export interface BaseRouteParams {
  bankId: string
  effectiveMonth: string
  accountCode: string
}

export interface StatementPackageRouteParams extends BaseRouteParams {
  packageNumber: string
}
